'use server';

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const systemRoutes = [
  '/dashboard',
  '/orders',
  '/users/admins',
  '/users/customers',
  '/users/profiles',
  '/tripcash',
  '/suppliers/providers',
  '/suppliers/channels',
  '/suppliers/providers-accounts',
  '/suppliers/rates',
  '/invoices/send',
  '/invoices',
  '/timi'
];

export function middleware(request: NextRequest) {
  const session = request.cookies.get('session');
  const isSystemRoute = systemRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  );
  const isLoginPage = request.nextUrl.pathname === '/';

  if (isSystemRoute && !session) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  if (isLoginPage && session) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
};
